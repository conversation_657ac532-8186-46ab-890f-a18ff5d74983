import { defineConfig } from '@umijs/max';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},

  routes: [
    {
      path: '/',
      redirect: '/account',
    },
    {
      name: 'Account',
      path: '/account',
      component: './Home',
      icon: 'UserOutlined',
    },
    {
      name: 'Send',
      path: '/send',
      component: './Access',
      icon: 'SendOutlined',
    },
    {
      name: 'Transactions',
      path: '/transactions',
      component: './Table',
      icon: 'FileTextOutlined',
    },
  ],
  npmClient: 'pnpm',
});

