import React from 'react';
import { Space, Avatar, Dropdown, Typography, Button } from 'antd';
import { WalletOutlined, UserOutlined, DisconnectOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';

const { Text } = Typography;

const RightContent: React.FC = () => {
  // 写死的测试数据
  const connected = true;
  const walletAddress = 'HVx9...Fuhr';
  const balance = '0.0140';

  // 用户菜单项
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'balance',
      label: (
        <div style={{ padding: '8px 0' }}>
          <Text strong>钱包余额</Text>
          <br />
          <Text type="secondary">{balance} SOL</Text>
        </div>
      ),
      disabled: true,
    },
    {
      type: 'divider',
    },
    {
      key: 'disconnect',
      label: '断开连接',
      icon: <DisconnectOutlined />,
      onClick: () => {
        console.log('断开钱包连接');
      },
    },
  ];

  if (!connected) {
    return (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Button type="primary" icon={<WalletOutlined />}>
          连接钱包
        </Button>
      </div>
    );
  }

  return (
    <Space size="middle">
      <Dropdown
        menu={{ items: userMenuItems }}
        placement="bottomRight"
        arrow
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer',
            padding: '4px 12px',
            borderRadius: '6px',
            transition: 'background-color 0.2s',
            border: '1px solid #d9d9d9',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#f5f5f5';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        >
          <Avatar
            size="small"
            icon={<WalletOutlined />}
            style={{ backgroundColor: '#1890ff', marginRight: '8px' }}
          />
          <div>
            <div style={{ fontSize: '14px', fontWeight: 500 }}>
              {walletAddress}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {balance} SOL
            </div>
          </div>
        </div>
      </Dropdown>
    </Space>
  );
};

export default RightContent;
