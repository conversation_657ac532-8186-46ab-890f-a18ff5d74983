import React, { useState, useEffect } from 'react';
import { Space, Spin, Avatar, Dropdown, Typography } from 'antd';
import { WalletOutlined, UserOutlined } from '@ant-design/icons';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import type { MenuProps } from 'antd';

const { Text } = Typography;

const RightContent: React.FC = () => {
  const { connected, publicKey, disconnect } = useWallet();
  const [balance, setBalance] = useState<number>(0);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (connected && publicKey) {
      const fetchBalance = async () => {
        try {
          setLoading(true);
          // 这里可以调用后端 API 获取余额
          // 暂时使用模拟数据
          await new Promise(resolve => setTimeout(resolve, 1000));
          setBalance(0.0140);
        } catch (error) {
          console.error('获取余额失败:', error);
          setBalance(0);
        } finally {
          setLoading(false);
        }
      };

      fetchBalance();
    } else {
      setBalance(0);
    }
  }, [connected, publicKey]);

  const formatAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  // 用户菜单项
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'balance',
      label: (
        <div style={{ padding: '8px 0' }}>
          <Text strong>余额</Text>
          <br />
          <Text type="secondary">
            {loading ? <Spin size="small" /> : `${balance.toFixed(4)} SOL`}
          </Text>
        </div>
      ),
      disabled: true,
    },
    {
      type: 'divider',
    },
    {
      key: 'disconnect',
      label: '断开连接',
      onClick: () => disconnect(),
    },
  ];

  if (!connected) {
    return (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <WalletMultiButton style={{ height: '32px' }} />
      </div>
    );
  }

  return (
    <Space size="middle">
      <Dropdown
        menu={{ items: userMenuItems }}
        placement="bottomRight"
        arrow
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer',
            padding: '4px 8px',
            borderRadius: '6px',
            transition: 'background-color 0.2s',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#f5f5f5';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        >
          <Avatar
            size="small"
            icon={<UserOutlined />}
            style={{ backgroundColor: '#1890ff', marginRight: '8px' }}
          />
          <div>
            <div style={{ fontSize: '14px', fontWeight: 500 }}>
              {publicKey ? formatAddress(publicKey.toBase58()) : ''}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {loading ? (
                <Spin size="small" />
              ) : (
                `${balance.toFixed(4)} SOL`
              )}
            </div>
          </div>
        </div>
      </Dropdown>
    </Space>
  );
};

export default RightContent;
