// 运行时配置
import React from 'react';
import RightContent from '@/components/RightContent';

// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
export async function getInitialState(): Promise<{ name: string }> {
  return { name: 'Squads Wallet' };
}

export const layout = () => {
  return {
    title: 'Squads Wallet',
    logo: false,
    menu: {
      locale: false,
    },
    rightContentRender: () => <RightContent />,
    headerContentRender: false, // 不显示页面标题
    footerRender: false, // 不显示页脚
  };
};
