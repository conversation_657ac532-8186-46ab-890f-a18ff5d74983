// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/","redirect":"/account","parentId":"ant-design-pro-layout","id":"1"},"2":{"name":"Account","path":"/account","icon":"UserOutlined","parentId":"ant-design-pro-layout","id":"2"},"3":{"name":"Send","path":"/send","icon":"SendOutlined","parentId":"ant-design-pro-layout","id":"3"},"4":{"name":"Transactions","path":"/transactions","icon":"FileTextOutlined","parentId":"ant-design-pro-layout","id":"4"},"ant-design-pro-layout":{"id":"ant-design-pro-layout","path":"/","isLayout":true}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import('./EmptyRoute')),
'2': React.lazy(() => import(/* webpackChunkName: "p__Home__index" */'@/pages/Home/index.tsx')),
'3': React.lazy(() => import(/* webpackChunkName: "p__Access__index" */'@/pages/Access/index.tsx')),
'4': React.lazy(() => import(/* webpackChunkName: "p__Table__index" */'@/pages/Table/index.tsx')),
'ant-design-pro-layout': React.lazy(() => import(/* webpackChunkName: "umi__plugin-layout__Layout" */'/Users/<USER>/Me/work/10_wallet_self/2_squads/2_code/squads-beta/fe-v3/src/.umi/plugin-layout/Layout.tsx')),
},
  };
}
