// @ts-nocheck
// This file is generated by Umi automatically
// DO NOT CHANGE IT MANUALLY!
// defineApp
export { defineApp } from './core/defineApp'
export type { RuntimeConfig } from './core/defineApp'
// plugins
export { Access, useAccess, useAccessMarkedRoutes } from '/Users/<USER>/Me/work/10_wallet_self/2_squads/2_code/squads-beta/fe-v3/src/.umi/plugin-access';
export { Provider, useModel } from '/Users/<USER>/Me/work/10_wallet_self/2_squads/2_code/squads-beta/fe-v3/src/.umi/plugin-model';
export { useRequest, UseRequestProvider, request, getRequestInstance } from '/Users/<USER>/Me/work/10_wallet_self/2_squads/2_code/squads-beta/fe-v3/src/.umi/plugin-request';
// plugins types.d.ts
export * from '/Users/<USER>/Me/work/10_wallet_self/2_squads/2_code/squads-beta/fe-v3/src/.umi/plugin-access/types.d';
export * from '/Users/<USER>/Me/work/10_wallet_self/2_squads/2_code/squads-beta/fe-v3/src/.umi/plugin-antd/types.d';
export * from '/Users/<USER>/Me/work/10_wallet_self/2_squads/2_code/squads-beta/fe-v3/src/.umi/plugin-request/types.d';
// @umijs/renderer-*
export { createBrowserHistory, createHashHistory, createMemoryHistory, Helmet, HelmetProvider, createSearchParams, generatePath, matchPath, matchRoutes, Navigate, NavLink, Outlet, resolvePath, useLocation, useMatch, useNavigate, useOutlet, useOutletContext, useParams, useResolvedPath, useRoutes, useSearchParams, useAppData, useClientLoaderData, useLoaderData, useRouteProps, useSelectedRoutes, useServerLoaderData, renderClient, __getRoot, Link, useRouteData, __useFetcher, withRouter } from '/Users/<USER>/Me/work/10_wallet_self/2_squads/2_code/squads-beta/fe-v3/node_modules/.pnpm/@umijs+renderer-react@4.4.11_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@umijs/renderer-react';
export type { History, ClientLoader } from '/Users/<USER>/Me/work/10_wallet_self/2_squads/2_code/squads-beta/fe-v3/node_modules/.pnpm/@umijs+renderer-react@4.4.11_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@umijs/renderer-react'
// umi/client/client/plugin
export { ApplyPluginsType, PluginManager } from '/Users/<USER>/Me/work/10_wallet_self/2_squads/2_code/squads-beta/fe-v3/node_modules/.pnpm/umi@4.4.11_@babel+core@7.28.0_@types+node@24.1.0_@types+react@18.3.23_eslint@8.35.0_lightning_53fu7txv2euncon6647th4xxg4/node_modules/umi/client/client/plugin.js';
export { history, createHistory } from './core/history';
export { terminal } from './core/terminal';
// react ssr
export const useServerInsertedHTML: Function = () => {};
// test
export { TestBrowser } from './testBrowser';
