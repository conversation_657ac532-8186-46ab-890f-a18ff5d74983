// 运行时配置
import './polyfills';
import React, { useState, useEffect } from 'react';
import { RunTimeLayoutConfig } from '@umijs/max';
import { Button, Space, Typography, Spin } from 'antd';
import { WalletOutlined, DisconnectOutlined } from '@ant-design/icons';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { SolanaWalletProvider } from './components/WalletProvider';

const { Text } = Typography;

// 自定义右上角内容组件
const RightContent: React.FC = () => {
  const { connected, publicKey } = useWallet();
  const [balance, setBalance] = useState<number>(0);
  const [loading, setLoading] = useState(false);

  // 添加调试信息
  console.log('RightContent 渲染状态:', { connected, publicKey: publicKey?.toBase58() });

  useEffect(() => {
    if (connected && publicKey) {
      const fetchBalance = async () => {
        try {
          setLoading(true);
          const response = await fetch('http://localhost:3002/api/multisigs', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            }
          });
          if (response.ok) {
            const data = await response.json();
            // 计算所有多签账户的总 SOL 余额
            let totalBalance = 0;
            data.multisigs.forEach((multisig: any) => {
              totalBalance += multisig.vault.balanceSOL || 0;
            });
            setBalance(totalBalance);
          } else {
            setBalance(0.0140);
          }
        } catch (error) {
          setBalance(0.0140);
        } finally {
          setLoading(false);
        }
      };

      fetchBalance();
    } else {
      setBalance(0);
    }
  }, [connected, publicKey]);

  const formatAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  return (
    <Space size="middle">
      {/* 调试信息 */}
      <div style={{ fontSize: '10px', color: '#999' }}>
        Debug: {connected ? 'Connected' : 'Not Connected'}
      </div>
      {connected && publicKey && (
        <Space>
          <WalletOutlined style={{ color: '#1890ff' }} />
          <div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {formatAddress(publicKey.toBase58())}
            </div>
            <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
              {loading ? (
                <Spin size="small" />
              ) : (
                `${balance.toFixed(4)} SOL`
              )}
            </div>
          </div>
        </Space>
      )}
      <WalletMultiButton />
    </Space>
  );
};

// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
export async function getInitialState(): Promise<{ name: string }> {
  return { name: 'Squads Network' };
}

// umi layout 运行时配置
export const layout: RunTimeLayoutConfig = () => {
  return {
    title: 'Squads Wallet',
    logo: false,
    menu: {
      locale: false,
    },
    rightContentRender: () => <RightContent />,
    // 可以进一步自定义其他部分
    headerContentRender: false, // 不显示页面标题
    footerRender: false, // 不显示页脚
  };
};

// 全局包装器，为整个应用提供钱包上下文
export function rootContainer(container: React.ReactElement) {
  return React.createElement(SolanaWalletProvider, {}, container);
}
