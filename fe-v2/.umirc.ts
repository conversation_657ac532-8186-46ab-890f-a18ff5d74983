import { defineConfig } from '@umijs/max';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  // 使用 umi 原生 layout
  layout: {
    title: 'Squads Wallet',
    locale: false,
  },
  routes: [
    {
      path: '/',
      redirect: '/account',
    },
    {
      name: 'Account',
      path: '/account',
      component: './Account',
      icon: 'UserOutlined',
    },
    {
      name: 'Send',
      path: '/send',
      component: './Send',
      icon: 'SendOutlined',
    },
    {
      name: 'Transactions',
      path: '/transactions',
      component: './Transactions',
      icon: 'FileTextOutlined',
    },
    {
      name: 'Members',
      path: '/members',
      component: './Members',
      icon: 'TeamOutlined',
    },
    {
      name: 'Settings',
      path: '/settings',
      component: './Settings',
      icon: 'SettingOutlined',
    },
  ],
  npmClient: 'pnpm',
  define: {
    'process.env': process.env,
  },
});

